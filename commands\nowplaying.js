const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('nowplaying')
        .setDescription('Xem bài hát đang phát'),

    async execute(interaction) {
        try {
            const queue = interaction.client.musicManager.getQueue(interaction.guild.id);
            const currentSong = interaction.client.musicManager.getNowPlaying(interaction.guild.id);

            if (!currentSong || !queue) {
                return await interaction.reply({
                    content: '❌ Không có bài hát nào đang phát!',
                    ephemeral: true
                });
            }

            // Lấy thông tin thời gian phát
            const timestamp = queue.node.getTimestamp();
            const currentTime = timestamp ? timestamp.current.label : '00:00';
            const totalTime = timestamp ? timestamp.total.label : currentSong.duration || '00:00';

            // Đảm bảo progress luôn trong khoảng 0-1
            let progress = 0;
            if (timestamp && timestamp.progress !== undefined && timestamp.progress !== null) {
                progress = Math.max(0, Math.min(1, timestamp.progress));
            }

            // Tạo progress bar
            const progressBar = this.createProgressBar(progress);

            // Lấy thông tin queue
            const queueList = interaction.client.musicManager.getQueueList(interaction.guild.id);
            const queuePosition = queueList.length > 0 ? `${queueList.length} bài trong queue` : 'Không có bài tiếp theo';

            const embed = new EmbedBuilder()
                .setColor('#1DB954') // Spotify green color
                .setTitle('🎵 Now Playing')
                .setDescription(`**${currentSong.title}**\n${currentSong.author || currentSong.artist || 'Unknown Artist'}`)
                .addFields(
                    {
                        name: 'Position',
                        value: `\`${currentTime}\``,
                        inline: true
                    },
                    {
                        name: 'Length',
                        value: `\`${totalTime}\``,
                        inline: true
                    },
                    {
                        name: 'Position in queue',
                        value: queuePosition,
                        inline: true
                    },
                    {
                        name: '\u200b', // Invisible character for spacing
                        value: `${progressBar}\n\`${currentTime}\` ${this.createProgressDots(progress)} \`${totalTime}\``,
                        inline: false
                    },
                    {
                        name: '👤 Requested by',
                        value: `${currentSong.requestedBy}`,
                        inline: true
                    }
                )
                .setTimestamp();

            // Thêm thumbnail nếu có
            if (currentSong.thumbnail) {
                embed.setThumbnail(currentSong.thumbnail);
            }

            // Thêm footer với thông tin nguồn
            const source = currentSong.source || currentSong.extractor?.identifier || 'Unknown';
            embed.setFooter({
                text: `Source: ${source}`,
                iconURL: interaction.client.user.displayAvatarURL()
            });

            await interaction.reply({ embeds: [embed] });

        } catch (error) {
            console.error('❌ Lỗi trong command nowplaying:', error);
            await interaction.reply({
                content: '❌ Có lỗi xảy ra khi hiển thị bài hát hiện tại!',
                ephemeral: true
            });
        }
    },

    // Tạo progress bar
    createProgressBar(progress) {
        // Đảm bảo progress luôn hợp lệ
        const safeProgress = Math.max(0, Math.min(1, progress || 0));

        const totalBars = 20;
        const filledBars = Math.max(0, Math.min(totalBars, Math.round(safeProgress * totalBars)));
        const emptyBars = Math.max(0, totalBars - filledBars);

        const progressChar = '▰';
        const emptyChar = '▱';

        return progressChar.repeat(filledBars) + emptyChar.repeat(emptyBars);
    },

    // Tạo progress dots (alternative style)
    createProgressDots(progress) {
        // Đảm bảo progress luôn hợp lệ
        const safeProgress = Math.max(0, Math.min(1, progress || 0));

        const totalDots = 30;
        const filledDots = Math.max(0, Math.min(totalDots, Math.round(safeProgress * totalDots)));
        const emptyDots = Math.max(0, totalDots - filledDots);

        return '●'.repeat(filledDots) + '○'.repeat(emptyDots);
    }
};
