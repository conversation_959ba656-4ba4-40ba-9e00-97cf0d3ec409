const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('help')
        .setDescription('Hiển thị danh sách các lệnh có sẵn'),
    
    async execute(interaction) {
        const embed = new EmbedBuilder()
            .setColor(0xFFD700)
            .setTitle('📚 Danh sách lệnh')
            .setDescription('Dưới đây là các lệnh bạn có thể sử dụng:')
            .addFields(
                {
                    name: '🏓 /ping',
                    value: 'Kiểm tra độ trễ của bot',
                    inline: false
                },
                {
                    name: 'ℹ️ /info',
                    value: 'Hiển thị thông tin về bot, server hoặc user',
                    inline: false
                },
                {
                    name: '🎵 /play',
                    value: '<PERSON><PERSON><PERSON> nhạc từ YouTube, Spotify, SoundCloud',
                    inline: false
                },
                {
                    name: '⏸️ /pause',
                    value: 'Tạm dừng nhạc đang phát',
                    inline: false
                },
                {
                    name: '▶️ /resume',
                    value: 'Tiế<PERSON> tục phát nhạc',
                    inline: false
                },
                {
                    name: '⏭️ /skip',
                    value: 'Bỏ qua bài hiện tại',
                    inline: false
                },
                {
                    name: '🔀 /shuffle',
                    value: 'Trộn thứ tự playlist',
                    inline: false
                },
                {
                    name: '⏹️ /stop',
                    value: 'Dừng nhạc và xóa queue',
                    inline: false
                },
                {
                    name: '🎵 /nowplaying',
                    value: 'Xem bài đang phát',
                    inline: false
                },
                {
                    name: '📋 /queue',
                    value: 'Xem danh sách nhạc chờ (có phân trang cho queue dài)',
                    inline: false
                },
                {
                    name: '📝 /lyrics',
                    value: 'Xem lời bài hát đang phát hoặc tìm kiếm (có phân trang cho lời dài)',
                    inline: false
                },
                {
                    name: '🚪 /leave',
                    value: 'Bot rời khỏi voice channel',
                    inline: false
                },
                {
                    name: '❓ /help',
                    value: 'Hiển thị danh sách lệnh này',
                    inline: false
                }
            )
            .addFields({
                name: '📄 Tính năng phân trang',
                value: '• Queue và lyrics dài sẽ được chia thành nhiều trang\n• Sử dụng các nút ⏮️ ◀️ ▶️ ⏭️ để điều hướng\n• Nút ❌ để đóng pagination\n• Tự động hết hạn sau 5-10 phút',
                inline: false
            })
            .setFooter({
                text: 'Sử dụng / để xem các lệnh có sẵn',
                iconURL: interaction.client.user.displayAvatarURL()
            })
            .setTimestamp();
        
        await interaction.reply({ embeds: [embed] });
    },
};
